% 速度空间导数的详细解释程序
% 功能：详细解释"空间导数"的物理含义和几何意义
% 作者：声波测井正演模拟系统
% 日期：2025年8月3日

clc; clear; close all;

%% ==================== 空间导数概念详解 ====================
fprintf('=== 速度的空间导数详细解释 ===\n\n');

%% ==================== 1. 什么是"空间" ====================
fprintf('1. 什么是"空间"？\n');
fprintf('   在您的声波测井FDTD模拟中，"空间"指的是：\n\n');

fprintf('   • 物理空间坐标：\n');
fprintf('     - X方向：水平方向（井筒径向）\n');
fprintf('     - Z方向：垂直方向（井筒轴向/深度方向）\n');
fprintf('     - 在2D模拟中，主要关注X-Z平面\n\n');

fprintf('   • 网格空间：\n');
fprintf('     - dx：X方向网格间距\n');
fprintf('     - dz：Z方向网格间距\n');
fprintf('     - 每个网格点有坐标(i,j)，对应物理位置(x,z)\n\n');

%% ==================== 2. 空间导数的几何意义 ====================
fprintf('2. 空间导数的几何意义\n');
fprintf('   空间导数描述的是：某个物理量在空间中的变化率\n\n');

fprintf('   以速度为例：\n');
fprintf('   • ∂Vz/∂z：速度Vz沿Z方向的变化率\n');
fprintf('   • 物理含义：当我们沿着Z轴移动时，速度如何变化\n');
fprintf('   • 几何含义：速度-位置曲线的斜率\n\n');

%% ==================== 3. 具体到您的代码 ====================
fprintf('3. 具体到您的代码中\n');
fprintf('   让我们分析您代码中的具体实现：\n\n');

% 模拟您代码中的参数
dz = 0.015;  % 您代码中的空间步长
fprintf('   代码参数：\n');
fprintf('   • dz = %.3f米（Z方向网格间距）\n', dz);
fprintf('   • k, k+1：相邻的Z方向网格索引\n');
fprintf('   • Vz(k), Vz(k+1)：相邻网格点的Z方向速度\n\n');

fprintf('   计算过程：\n');
fprintf('   ```matlab\n');
fprintf('   local_strain_rate = (Vz(k+1, med_x) - Vz(k, med_x)) / dz;\n');
fprintf('   ```\n\n');

fprintf('   这里的含义：\n');
fprintf('   • Vz(k+1, med_x)：深度位置 z+dz 处的速度\n');
fprintf('   • Vz(k, med_x)：深度位置 z 处的速度\n');
fprintf('   • 差值：(Vz(k+1) - Vz(k))：速度在dz距离内的变化量\n');
fprintf('   • 除以dz：得到单位距离内的速度变化率\n\n');

%% ==================== 4. 直观理解示例 ====================
fprintf('4. 直观理解示例\n');
fprintf('   让我们用一个具体例子来理解：\n\n');

% 创建示例数据
z_positions = [1.500, 1.515, 1.530, 1.545, 1.560];  % 深度位置(米)
velocities = [0.1, 0.3, 0.8, 1.2, 1.0];  % 对应的速度值

fprintf('   假设在某个时刻，沿井筒深度方向的速度分布：\n');
for i = 1:length(z_positions)
    fprintf('   深度 %.3f米处：速度 = %.1f m/s\n', z_positions(i), velocities(i));
end
fprintf('\n');

% 计算空间导数
fprintf('   计算空间导数（应变率）：\n');
dz_example = z_positions(2) - z_positions(1);
for i = 1:length(z_positions)-1
    spatial_derivative = (velocities(i+1) - velocities(i)) / dz_example;
    fprintf('   位置 %.3f-%.3f米间：∂Vz/∂z = (%.1f-%.1f)/%.3f = %.1f s⁻¹\n', ...
            z_positions(i), z_positions(i+1), velocities(i+1), velocities(i), dz_example, spatial_derivative);
end
fprintf('\n');

%% ==================== 5. 物理意义深入解释 ====================
fprintf('5. 物理意义深入解释\n');
fprintf('   为什么要计算速度的空间导数？\n\n');

fprintf('   • 应变的定义：ε = ∂u/∂z（位移的空间导数）\n');
fprintf('   • 应变率的定义：ε̇ = ∂ε/∂t = ∂(∂u/∂z)/∂t = ∂(∂u/∂t)/∂z = ∂v/∂z\n');
fprintf('   • 因此：应变率 = 速度的空间导数\n\n');

fprintf('   声波传播时：\n');
fprintf('   • 不同位置的介质有不同的运动速度\n');
fprintf('   • 相邻位置速度的差异导致介质发生变形（应变）\n');
fprintf('   • 应变的变化率就是应变率\n');
fprintf('   • DAS检测的正是这种应变率\n\n');

%% ==================== 6. 与时间导数的对比 ====================
fprintf('6. 与时间导数的对比\n');
fprintf('   让我们对比空间导数和时间导数：\n\n');

fprintf('   空间导数 ∂Vz/∂z：\n');
fprintf('   • 固定时间，看不同位置的速度差异\n');
fprintf('   • 反映空间中速度场的不均匀性\n');
fprintf('   • 对应应变率（DAS测量的物理量）\n\n');

fprintf('   时间导数 ∂Vz/∂t：\n');
fprintf('   • 固定位置，看速度随时间的变化\n');
fprintf('   • 反映某点的加速度\n');
fprintf('   • 对应质点的运动状态变化\n\n');

%% ==================== 7. 在井筒环境中的具体含义 ====================
fprintf('7. 在井筒环境中的具体含义\n');
fprintf('   在声波测井中，空间导数的物理意义：\n\n');

fprintf('   • 井筒是一个柱状空间\n');
fprintf('   • Z方向：沿井筒轴向（深度方向）\n');
fprintf('   • 声波沿井筒传播时，不同深度处的速度不同\n');
fprintf('   • ∂Vz/∂z 描述了速度沿深度方向的变化\n');
fprintf('   • 这种变化反映了介质的变形状态\n\n');

%% ==================== 8. DAS光纤的空间分布 ====================
fprintf('8. DAS光纤的空间分布\n');
fprintf('   DAS光纤在井筒中的空间布置：\n\n');

fprintf('   • 光纤沿井筒轴向（Z方向）布置\n');
fprintf('   • 每个标距（gauge）覆盖一定的空间长度\n');
fprintf('   • 标距内的应变率 = 该段光纤长度变化率\n');
fprintf('   • 光纤长度变化 ∝ 速度的空间导数\n\n');

%% ==================== 9. 数学表达式总结 ====================
fprintf('9. 数学表达式总结\n');
fprintf('   完整的数学关系：\n\n');

fprintf('   • 位移场：u(z,t)\n');
fprintf('   • 速度场：v(z,t) = ∂u(z,t)/∂t\n');
fprintf('   • 应变场：ε(z,t) = ∂u(z,t)/∂z\n');
fprintf('   • 应变率：ε̇(z,t) = ∂ε(z,t)/∂t = ∂v(z,t)/∂z\n\n');

fprintf('   有限差分近似：\n');
fprintf('   • ∂v/∂z ≈ (v(z+dz) - v(z))/dz\n');
fprintf('   • 在网格中：∂Vz/∂z ≈ (Vz(k+1) - Vz(k))/dz\n\n');

%% ==================== 10. 可视化演示 ====================
fprintf('10. 生成可视化演示\n');
create_spatial_derivative_visualization();
fprintf('    空间导数概念演示图已生成\n\n');

fprintf('=== 总结 ===\n');
fprintf('"空间"指的是物理空间中的位置坐标（特别是Z方向深度）\n');
fprintf('"空间导数"指的是物理量沿空间位置的变化率\n');
fprintf('在DAS应变率计算中，就是速度沿井筒深度方向的变化率\n\n');

%% ==================== 可视化函数 ====================
function create_spatial_derivative_visualization()
    % 创建空间导数概念可视化演示
    
    figure('Position', [100, 100, 1400, 1000], 'Color', 'w');
    
    % 子图1：空间中的速度分布
    subplot(2, 3, 1);
    z = 0:0.02:2;  % 深度坐标
    Vz = 0.5 * sin(3*pi*z) + 0.3 * cos(5*pi*z);  % 模拟速度分布
    plot(z, Vz, 'b-', 'LineWidth', 2);
    xlabel('深度 z (m)');
    ylabel('速度 Vz (m/s)');
    title('速度沿深度的分布');
    grid on;
    
    % 子图2：空间导数计算
    subplot(2, 3, 2);
    dz = z(2) - z(1);
    spatial_deriv = gradient(Vz, dz);  % 计算空间导数
    plot(z, spatial_deriv, 'r-', 'LineWidth', 2);
    xlabel('深度 z (m)');
    ylabel('∂Vz/∂z (s⁻¹)');
    title('速度的空间导数（应变率）');
    grid on;
    
    % 子图3：有限差分示意
    subplot(2, 3, 3);
    z_sample = z(1:5:end);  % 采样点
    Vz_sample = Vz(1:5:end);
    plot(z_sample, Vz_sample, 'bo-', 'LineWidth', 2, 'MarkerSize', 8);
    hold on;
    % 显示差分计算
    for i = 1:length(z_sample)-1
        plot([z_sample(i), z_sample(i+1)], [Vz_sample(i), Vz_sample(i+1)], 'r--', 'LineWidth', 1);
        mid_z = (z_sample(i) + z_sample(i+1))/2;
        slope = (Vz_sample(i+1) - Vz_sample(i))/(z_sample(i+1) - z_sample(i));
        text(mid_z, (Vz_sample(i) + Vz_sample(i+1))/2 + 0.1, ...
             sprintf('%.1f', slope), 'HorizontalAlignment', 'center', 'FontSize', 8);
    end
    xlabel('深度 z (m)');
    ylabel('速度 Vz (m/s)');
    title('有限差分计算示意');
    grid on;
    
    % 子图4：井筒中的空间概念
    subplot(2, 3, 4);
    % 绘制井筒示意图
    z_well = 0:0.1:2;
    x_well = 0.1 * ones(size(z_well));  % 井筒半径
    plot(x_well, z_well, 'k-', 'LineWidth', 3);
    hold on;
    plot(-x_well, z_well, 'k-', 'LineWidth', 3);
    
    % 显示DAS光纤
    plot(zeros(size(z_well)), z_well, 'r-', 'LineWidth', 2);
    
    % 标注空间方向
    quiver(0, 0.5, 0, 0.5, 'k', 'LineWidth', 2, 'MaxHeadSize', 0.3);
    text(0.05, 1, 'Z方向\n(深度)', 'HorizontalAlignment', 'left', 'FontSize', 10);
    
    xlabel('X方向 (m)');
    ylabel('Z方向深度 (m)');
    title('井筒中的空间坐标');
    axis equal;
    grid on;
    set(gca, 'YDir', 'reverse');  % 深度向下
    
    % 子图5：DAS标距示意
    subplot(2, 3, 5);
    z_gauge = 1.0:0.1:1.5;  % 标距范围
    gauge_length = 0.1;  % 标距长度
    
    % 绘制标距
    for i = 1:length(z_gauge)-1
        rectangle('Position', [-0.02, z_gauge(i), 0.04, gauge_length], ...
                 'FaceColor', [0.8, 0.8, 1], 'EdgeColor', 'b');
        text(0.05, z_gauge(i) + gauge_length/2, sprintf('标距%d', i), ...
             'FontSize', 8, 'VerticalAlignment', 'middle');
    end
    
    xlabel('X方向 (m)');
    ylabel('Z方向深度 (m)');
    title('DAS标距空间分布');
    grid on;
    set(gca, 'YDir', 'reverse');
    
    % 子图6：应变率物理意义
    subplot(2, 3, 6);
    % 绘制变形示意
    z_orig = [1.0, 1.1, 1.2, 1.3, 1.4];
    z_deformed = [1.0, 1.12, 1.25, 1.35, 1.42];
    
    plot(zeros(size(z_orig)), z_orig, 'bo-', 'LineWidth', 2, 'MarkerSize', 8, 'DisplayName', '原始位置');
    hold on;
    plot(0.1*ones(size(z_deformed)), z_deformed, 'ro-', 'LineWidth', 2, 'MarkerSize', 8, 'DisplayName', '变形后位置');
    
    % 绘制位移箭头
    for i = 1:length(z_orig)
        if abs(z_deformed(i) - z_orig(i)) > 0.01
            quiver(0, z_orig(i), 0.1, z_deformed(i) - z_orig(i), 'g', 'LineWidth', 1.5, 'MaxHeadSize', 0.5);
        end
    end
    
    xlabel('X方向');
    ylabel('Z方向深度 (m)');
    title('介质变形与应变率');
    legend('Location', 'best');
    grid on;
    set(gca, 'YDir', 'reverse');
    
    sgtitle('速度空间导数概念详解', 'FontSize', 16, 'FontWeight', 'bold');
    
    % 保存图片
    saveas(gcf, '速度空间导数概念演示.png');
end
