% 简化的偶极子数据绘图程序
% 专门用于测试和验证偶极子数据

clear; clc;

% 加载偶极子数据
data_file = 'OuJi_3D_Results_20250803_132220.mat';
fprintf('加载数据文件: %s\n', data_file);
load(data_file);

% 显示基本信息
fprintf('\n=== 数据基本信息 ===\n');
fprintf('检波器数据尺寸: %d × %d\n', size(rec, 1), size(rec, 2));
fprintf('DAS数据尺寸: %d × %d\n', size(das_strain_rate, 1), size(das_strain_rate, 2));
fprintf('时间步数: %d, 时间步长: %.2e秒\n', NSTEP, DELTAT);

% 显示偶极子配置信息
if exist('dipole_source_info', 'var')
    fprintf('\n=== 偶极子配置 ===\n');
    fprintf('激发方向: %s\n', dipole_source_info.excitation_direction);
    fprintf('接收方向: %s\n', dipole_source_info.receiver_direction);
    fprintf('配置类型: %s\n', dipole_source_info.configuration_type);
    fprintf('激发描述: %s\n', dipole_source_info.excitation_description);
end

% 数据统计
fprintf('\n=== 数据统计 ===\n');
fprintf('检波器数据:\n');
for i = 1:8
    fprintf('  检波器%d: max=%.6e, std=%.6e\n', i, max(abs(rec(i,:))), std(rec(i,:)));
end

fprintf('DAS数据: max=%.6e, std=%.6e\n', max(abs(das_strain_rate(:))), std(das_strain_rate(:)));

% 时间轴
time_axis = (0:NSTEP-1) * DELTAT * 1000;  % 转换为毫秒

% 振幅增强因子
geophone_boost = 1e12;  % 检波器增强因子
das_boost = 1e3;        % DAS增强因子

% 创建图形
figure('Position', [100, 100, 1400, 1000], 'Color', 'w');

%% 绘制检波器数据
subplot(2,1,1);
hold on;

% 绘制每个检波器的数据
colors = lines(8);
for i = 1:8
    % 增强并偏移数据
    enhanced_data = rec(i,:) * geophone_boost;
    offset = (i-1) * 0.2;  % 垂直偏移
    
    plot(time_axis, enhanced_data + offset, 'Color', colors(i,:), 'LineWidth', 1.5);
    
    % 添加基线
    plot([time_axis(1), time_axis(end)], [offset, offset], 'k--', 'LineWidth', 0.5, 'Color', [0.7, 0.7, 0.7]);
    
    % 添加标签
    text(time_axis(end)*1.02, offset, sprintf('检波器%d (%.3fm)', i, geophone_depths(i)), ...
         'FontSize', 10, 'VerticalAlignment', 'middle');
end

xlabel('时间 (ms)', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('深度偏移', 'FontSize', 12, 'FontWeight', 'bold');

if exist('dipole_source_info', 'var')
    title(sprintf('偶极子检波器数据 (%s激发-%s接收, 增强因子: %.0e)', ...
                  upper(dipole_source_info.excitation_direction), ...
                  upper(dipole_source_info.receiver_direction), ...
                  geophone_boost), 'FontSize', 14, 'FontWeight', 'bold');
else
    title(sprintf('检波器数据 (增强因子: %.0e)', geophone_boost), 'FontSize', 14, 'FontWeight', 'bold');
end

grid on;
xlim([0, 5]);  % 显示前5ms
ylim([-0.2, 1.6]);

%% 绘制DAS数据（选择与检波器对应的标距）
subplot(2,1,2);
hold on;

% 选择与检波器对应的DAS标距
das_indices = [2, 7, 12, 17, 22, 27, 32, 37];

for i = 1:8
    das_idx = das_indices(i);
    
    % 增强并偏移数据
    enhanced_data = das_strain_rate(das_idx,:) * das_boost;
    offset = (i-1) * 0.2;  % 垂直偏移
    
    plot(time_axis, enhanced_data + offset, 'Color', colors(i,:), 'LineWidth', 1.5);
    
    % 添加基线
    plot([time_axis(1), time_axis(end)], [offset, offset], 'k--', 'LineWidth', 0.5, 'Color', [0.7, 0.7, 0.7]);
    
    % 添加标签
    text(time_axis(end)*1.02, offset, sprintf('DAS%d (%.3fm)', das_idx, das_depths(das_idx)), ...
         'FontSize', 10, 'VerticalAlignment', 'middle');
end

xlabel('时间 (ms)', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('深度偏移', 'FontSize', 12, 'FontWeight', 'bold');
title(sprintf('DAS应变率数据 (增强因子: %.0e)', das_boost), 'FontSize', 14, 'FontWeight', 'bold');

grid on;
xlim([0, 5]);  % 显示前5ms
ylim([-0.2, 1.6]);

%% 保存图片
if exist('dipole_source_info', 'var')
    filename = sprintf('偶极子数据_%s激发_%s接收.png', ...
                       upper(dipole_source_info.excitation_direction), ...
                       upper(dipole_source_info.receiver_direction));
else
    filename = '偶极子数据_测试.png';
end

print(gcf, filename, '-dpng', '-r300');
fprintf('\n图片已保存: %s\n', filename);

%% 数据验证
fprintf('\n=== 数据验证 ===\n');

% 检查是否有明显的波形
geophone_has_signal = false;
das_has_signal = false;

for i = 1:8
    if max(abs(rec(i,:))) > 1e-15
        geophone_has_signal = true;
        break;
    end
end

if max(abs(das_strain_rate(:))) > 1e-15
    das_has_signal = true;
end

fprintf('检波器数据有信号: %s\n', mat2str(geophone_has_signal));
fprintf('DAS数据有信号: %s\n', mat2str(das_has_signal));

if geophone_has_signal
    fprintf('检波器数据看起来正常\n');
else
    fprintf('警告: 检波器数据可能有问题\n');
end

if das_has_signal
    fprintf('DAS数据看起来正常\n');
else
    fprintf('警告: DAS数据可能有问题\n');
end

fprintf('\n=== 绘图完成 ===\n');
