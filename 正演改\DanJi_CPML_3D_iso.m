% Parameters
% Grid dimensions
NX = 100;
NY = 100;
NZ = 500;


% Grid spacing
DELTAX = 0.01;
DELTAY = DELTAX;
DELTAZ = DELTAX;
ONE_OVER_DELTAX = 1.0 / DELTAX;
ONE_OVER_DELTAY = ONE_OVER_DELTAX;
ONE_OVER_DELTAZ = ONE_OVER_DELTAX;


% 定义圆形区域的半径
radius = 0.1; % 圆形半径为0.1m

center_x = ((NX)/2) ; % 圆形中心的x坐标
center_y = ((NY)/2) ; % 圆形中心的y坐标
% P-velocity, S-velocity and density
vf_p=1500;
vf_s=0;
rho_f=1000;
cp = 3000;
cs = 1200;
rho = 2500;
VP = cp * ones(NX, NY, NZ);
VS = cs * ones(NX, NY, NZ);
RHO = rho * ones(NX, NY, NZ);
for i = 1:NX
for j = 1:NY
for k = 1:NZ
% 计算当前点x和y坐标
% 判断当前点是否在圆形区域内
if sqrt((i - center_x)^2 + (j - center_y)^2) < radius/DELTAX
VP(i, j, k) = vf_p;
VS(i, j, k) = vf_s;
RHO(i, j, k) = rho_f;
end
end
end
end
plot_3d_slices(RHO, 0.5, 0.5, 0.5)
%%
mu = RHO .* VS.^2;
lambda = RHO.* (VP.^2 - 2.0 * VS.^2);
lambdaplustwomu = RHO .* VP.^2; 
 
 
 % RHO 密度场平均
RHO_x_avg = RHO;  % x 方向密度平均
RHO_y_avg = RHO;  % y 方向密度平均
RHO_z_avg = RHO;  % z 方向密度平均

RHO_x_avg(1:NX-1,:,:)=(RHO(1:NX-1,:,:)+RHO(2:NX,:,:))/2;
RHO_y_avg(:,1:NY-1,:)=(RHO(:,1:NY-1,:)+RHO(:,2:NY,:))/2;
RHO_z_avg(:,:,1:NZ-1)=(RHO(:,:,1:NZ-1)+RHO(:,:,2:NZ))/2;

mu_xy_avg = mu; % x-y 平面的切变模量平均
mu_xz_avg = mu; % x-z 平面的切变模量平均
mu_yz_avg = mu; % y-z 平面的切变模量平均


for i = 2:NX
    for j = 1:NY-1
        for k = 1:NZ
            if all(mu(i-1:i, j:j+1, k) ~= 0)
                mu_xy_avg(i,j,k)=4/(1/mu(i,j,k)+1/mu(i,j+1,k)+1/mu(i-1,j,k)+1/mu(i-1,j+1,k));
            else
                mu_xy_avg(i, j, k) = 0;
            end
        end
    end
end

for i = 2:NX
    for j = 1:NY
        for k = 1:NZ-1
            if all(mu(i-1:i, j, k:k+1) ~= 0)
                mu_xz_avg(i,j,k)=4/(1/mu(i,j,k+1)+1/mu(i,j,k)+1/mu(i-1,j,k+1)+1/mu(i-1,j,k));
            else
                mu_xz_avg(i, j, k) = 0;
            end
        end
    end
end

for i = 1:NX
    for j = 1:NY-1
        for k = 1:NZ-1
            if all(mu(i, j:j+1, k:k+1) ~= 0)
                mu_yz_avg(i, j, k)=4/(1/mu(i,j,k+1)+1/mu(i,j,k)+1/mu(i,j+1,k+1)+1/mu(i,j+1,k));
            else
                mu_yz_avg(i, j, k) = 0;
            end
        end
    end
end





% Time parameters
NSTEP = 5000;
DELTAT = 1e-6;

% Source parameters
f0 = 8000.0;
t0 = 1.20 / f0;
factor = 1e7;

% PML parameters
USE_PML_XMIN = true;
USE_PML_XMAX = true;
USE_PML_YMIN = true;
USE_PML_YMAX = true;
USE_PML_ZMIN = true;
USE_PML_ZMAX = true;
NPOINTS_PML = 20;

% Source location
ISOURCE = round(NX / 2) ;
JSOURCE = round(NY / 2) ;
KSOURCE = round(NZ / 10);  
xsource = (ISOURCE - 1) * DELTAX;
ysource = (JSOURCE - 1) * DELTAY;


% Constants
PI = 3.141592653589793;
DEGREES_TO_RADIANS = PI / 180.0;
ZERO = 0.0;
NPOWER = 2.0;
K_MAX_PML = 1.0;
ALPHA_MAX_PML = 2.0*PI*(f0/2.0);

% Precomputed constants
DELTAT_lambda = DELTAT * lambda;
DELTAT_mu_xy_avg = DELTAT * mu_xy_avg;
DELTAT_mu_xz_avg = DELTAT * mu_xz_avg;
DELTAT_mu_yz_avg = DELTAT * mu_yz_avg;
DELTAT_lambdaplus2mu = DELTAT * lambdaplustwomu;
DELTAT_over_rhox = DELTAT / RHO_x_avg;
DELTAT_over_rhoy = DELTAT / RHO_y_avg;
DELTAT_over_rhoz = DELTAT / RHO_z_avg;
%% Initialize arrays
% Main field arrays
vx = zeros(NX, NY, NZ);
vy = zeros(NX, NY, NZ);
vz = zeros(NX, NY, NZ);
sigmaxx = zeros(NX, NY, NZ);
sigmayy = zeros(NX, NY, NZ);
sigmazz = zeros(NX, NY, NZ);
sigmaxy = zeros(NX, NY, NZ);
sigmaxz = zeros(NX, NY, NZ);
sigmayz = zeros(NX, NY, NZ);

% PML memory variables
memory_dvx_dx = zeros(NX, NY, NZ);
memory_dvx_dy = zeros(NX, NY, NZ);
memory_dvx_dz = zeros(NX, NY, NZ);
memory_dvy_dx = zeros(NX, NY, NZ);
memory_dvy_dy = zeros(NX, NY, NZ);
memory_dvy_dz = zeros(NX, NY, NZ);
memory_dvz_dx = zeros(NX, NY, NZ);
memory_dvz_dy = zeros(NX, NY, NZ);
memory_dvz_dz = zeros(NX, NY, NZ);
memory_dsigmaxx_dx = zeros(NX, NY, NZ);
memory_dsigmayy_dy = zeros(NX, NY, NZ);
memory_dsigmazz_dz = zeros(NX, NY, NZ);
memory_dsigmaxy_dx = zeros(NX, NY, NZ);
memory_dsigmaxy_dy = zeros(NX, NY, NZ);
memory_dsigmaxz_dx = zeros(NX, NY, NZ);
memory_dsigmaxz_dz = zeros(NX, NY, NZ);
memory_dsigmayz_dy = zeros(NX, NY, NZ);
memory_dsigmayz_dz = zeros(NX, NY, NZ);

%% Setup PML profiles
thickness_PML_x = NPOINTS_PML * DELTAX;
thickness_PML_y = NPOINTS_PML * DELTAY;
thickness_PML_z = NPOINTS_PML * DELTAZ;

Rcoef = 0.001;
d0_x = -(NPOWER + 1) * cp * log(Rcoef) / (2.0 * thickness_PML_x);
d0_y = -(NPOWER + 1) * cp * log(Rcoef) / (2.0 * thickness_PML_y);
d0_z = -(NPOWER + 1) * cp * log(Rcoef) / (2.0 * thickness_PML_z);

% Initialize damping profiles
d_x = zeros(NX, 1);
d_x_half = zeros(NX, 1);
K_x = ones(NX, 1);
K_x_half = ones(NX, 1);
alpha_x = zeros(NX, 1);
alpha_x_half = zeros(NX, 1);
a_x = zeros(NX, 1);
a_x_half = zeros(NX, 1);
b_x = ones(NX, 1);  % Initialize to 1 instead of 0
b_x_half = ones(NX, 1);  % Initialize to 1 instead of 0

d_y = zeros(NY, 1);
d_y_half = zeros(NY, 1);
K_y = ones(NY, 1);
K_y_half = ones(NY, 1);
alpha_y = zeros(NY, 1);
alpha_y_half = zeros(NY, 1);
a_y = zeros(NY, 1);
a_y_half = zeros(NY, 1);
b_y = ones(NY, 1);  % Initialize to 1 instead of 0
b_y_half = ones(NY, 1);  % Initialize to 1 instead of 0

d_z = zeros(NZ, 1);
d_z_half = zeros(NZ, 1);
K_z = ones(NZ, 1);
K_z_half = ones(NZ, 1);
alpha_z = zeros(NZ, 1);
alpha_z_half = zeros(NZ, 1);
a_z = zeros(NZ, 1);
a_z_half = zeros(NZ, 1);
b_z = ones(NZ, 1);  % Initialize to 1 instead of 0
b_z_half = ones(NZ, 1);  % Initialize to 1 instead of 0

% Compute damping profiles for X direction
xoriginleft = thickness_PML_x;
xoriginright = (NX-1)*DELTAX - thickness_PML_x;

for i = 1:NX
    xval = DELTAX * (i-1);
    
    % xmin edge
    if USE_PML_XMIN
        abscissa_in_PML = xoriginleft - xval;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_x;
            d_x(i) = d0_x * abscissa_normalized^NPOWER;
            K_x(i) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_x(i) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
        
        abscissa_in_PML = xoriginleft - (xval + DELTAX/2.0);
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_x;
            d_x_half(i) = d0_x * abscissa_normalized^NPOWER;
            K_x_half(i) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_x_half(i) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
    end
    
    % xmax edge
    if USE_PML_XMAX
        abscissa_in_PML = xval - xoriginright;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_x;
            d_x(i) = d0_x * abscissa_normalized^NPOWER;
            K_x(i) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_x(i) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
        
        abscissa_in_PML = xval + DELTAX/2.0 - xoriginright;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_x;
            d_x_half(i) = d0_x * abscissa_normalized^NPOWER;
            K_x_half(i) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_x_half(i) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
    end
    
    alpha_x(i) = max(alpha_x(i), ZERO);
    alpha_x_half(i) = max(alpha_x_half(i), ZERO);
    
    b_x(i) = exp(-(d_x(i) / K_x(i) + alpha_x(i)) * DELTAT);
    b_x_half(i) = exp(-(d_x_half(i) / K_x_half(i) + alpha_x_half(i)) * DELTAT);
    
    if abs(d_x(i)) > 1e-6
        a_x(i) = d_x(i) * (b_x(i) - 1.0) / (K_x(i) * (d_x(i) + K_x(i) * alpha_x(i)));
    end
    if abs(d_x_half(i)) > 1e-6
        a_x_half(i) = d_x_half(i) * (b_x_half(i) - 1.0) / (K_x_half(i) * (d_x_half(i) + K_x_half(i) * alpha_x_half(i)));
    end
end

% Compute damping profiles for Y direction
yoriginbottom = thickness_PML_y;
yorigintop = (NY-1)*DELTAY - thickness_PML_y;

for j = 1:NY
    yval = DELTAY * (j-1);
    
    % ymin edge
    if USE_PML_YMIN
        abscissa_in_PML = yoriginbottom - yval;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_y;
            d_y(j) = d0_y * abscissa_normalized^NPOWER;
            K_y(j) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_y(j) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
        
        abscissa_in_PML = yoriginbottom - (yval + DELTAY/2.0);
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_y;
            d_y_half(j) = d0_y * abscissa_normalized^NPOWER;
            K_y_half(j) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_y_half(j) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
    end
    
    % ymax edge
    if USE_PML_YMAX
        abscissa_in_PML = yval - yorigintop;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_y;
            d_y(j) = d0_y * abscissa_normalized^NPOWER;
            K_y(j) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_y(j) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
        
        abscissa_in_PML = yval + DELTAY/2.0 - yorigintop;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_y;
            d_y_half(j) = d0_y * abscissa_normalized^NPOWER;
            K_y_half(j) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_y_half(j) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
    end
    
    b_y(j) = exp(-(d_y(j) / K_y(j) + alpha_y(j)) * DELTAT);
    b_y_half(j) = exp(-(d_y_half(j) / K_y_half(j) + alpha_y_half(j)) * DELTAT);
    
    if abs(d_y(j)) > 1e-6
        a_y(j) = d_y(j) * (b_y(j) - 1.0) / (K_y(j) * (d_y(j) + K_y(j) * alpha_y(j)));
    end
    if abs(d_y_half(j)) > 1e-6
        a_y_half(j) = d_y_half(j) * (b_y_half(j) - 1.0) / (K_y_half(j) * (d_y_half(j) + K_y_half(j) * alpha_y_half(j)));
    end
end

% Compute damping profiles for Z direction
zoriginbottom = thickness_PML_z;
zorigintop = (NZ-1)*DELTAZ - thickness_PML_z;

for k = 1:NZ
    zval = DELTAZ * (k-1);
    
    % zmin edge
    if USE_PML_ZMIN
        abscissa_in_PML = zoriginbottom - zval;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_z;
            d_z(k) = d0_z * abscissa_normalized^NPOWER;
            K_z(k) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_z(k) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
        
        abscissa_in_PML = zoriginbottom - (zval + DELTAZ/2.0);
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_z;
            d_z_half(k) = d0_z * abscissa_normalized^NPOWER;
            K_z_half(k) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_z_half(k) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
    end
    
    % zmax edge
    if USE_PML_ZMAX
        abscissa_in_PML = zval - zorigintop;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_z;
            d_z(k) = d0_z * abscissa_normalized^NPOWER;
            K_z(k) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_z(k) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
        
        abscissa_in_PML = zval + DELTAZ/2.0 - zorigintop;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_z;
            d_z_half(k) = d0_z * abscissa_normalized^NPOWER;
            K_z_half(k) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_z_half(k) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
    end
    
    b_z(k) = exp(-(d_z(k) / K_z(k) + alpha_z(k)) * DELTAT);
    b_z_half(k) = exp(-(d_z_half(k) / K_z_half(k) + alpha_z_half(k)) * DELTAT);
    
    if abs(d_z(k)) > 1e-6
        a_z(k) = d_z(k) * (b_z(k) - 1.0) / (K_z(k) * (d_z(k) + K_z(k) * alpha_z(k)));
    end
    if abs(d_z_half(k)) > 1e-6
        a_z_half(k) = d_z_half(k) * (b_z_half(k) - 1.0) / (K_z_half(k) * (d_z_half(k) + K_z_half(k) * alpha_z_half(k)));
    end
end

%% Check stability
Courant_number = cp * DELTAT * sqrt(1.0/DELTAX^2 + 1.0/DELTAY^2 + 1.0/DELTAZ^2);
if Courant_number > 1.0
    error('Time step is too large, simulation will be unstable');
end

% Check source indices
fprintf('Source indices: ISOURCE=%d, JSOURCE=%d, KSOURCE=%d\n', ISOURCE, JSOURCE, KSOURCE);
fprintf('Array dimensions: NX=%d, NY=%d, NZ=%d\n', NX, NY, NZ);

if ISOURCE < 1 || ISOURCE > NX || JSOURCE < 1 || JSOURCE > NY || KSOURCE < 1 || KSOURCE > NZ
    error('Source indices are out of bounds');
end

%% Main time loop
%Prepare for GIF creation
filename = 'wavefield_animation.gif';
delay_time = 0.1; % Delay between frames in seconds
frame_count = 0;

% Determine the figure size
fig = figure('Position', [100, 100, 300, 1200]);
rec=zeros(8,NSTEP);

% DAS系统参数设置（方案2：前移3cm，完美对应）
num_das_points = 37;          % DAS标距点数量
gauge_length = 0.06;          % 标距长度6cm
gauge_overlap = 0.5;          % 标距重合比例(50%重叠)
enable_das = true;            % DAS系统开关

% DAS数据存储初始化
if enable_das
    das_strain_rate = zeros(num_das_points, NSTEP);  % DAS应变率数据存储
    gauge_centers = zeros(num_das_points, 1);        % 每个标距中心点的Z位置（网格点）

    % DAS标距中心点位置计算（网格点坐标）
    % 前移3cm，确保与检波器完美对应
    gauge_spacing_grid = round((gauge_length * (1 - gauge_overlap)) / DELTAZ);  % 标距中心间距（网格点）= 3个网格点
    das_start_position = KSOURCE + 300 - round(0.03/DELTAZ);  % 前移3cm = 前移3个网格点

    for i = 1:num_das_points
        gauge_centers(i) = das_start_position + (i-1) * gauge_spacing_grid;
    end

    fprintf('=== DAS系统配置信息 ===\n');
    fprintf('标距点数量: %d\n', num_das_points);
    fprintf('标距长度: %.3f米\n', gauge_length);
    fprintf('标距重合比例: %.1f%%\n', gauge_overlap*100);
    fprintf('标距中心间距: %.3f米\n', gauge_length * (1 - gauge_overlap));
    fprintf('前移距离: %.3f米\n', 0.03);
    fprintf('覆盖范围: Z=%.3f米 - %.3f米\n', gauge_centers(1)*DELTAZ, gauge_centers(end)*DELTAZ);

    % 显示检波器与DAS对应关系
    fprintf('\n=== 检波器与DAS标距对应关系 ===\n');
    rec_positions = [300, 315, 330, 345, 360, 375, 390, 405];  % 检波器相对KSOURCE的偏移
    for i = 1:8
        rec_pos_grid = KSOURCE + rec_positions(i);  % 检波器网格位置
        rec_pos_meter = rec_pos_grid * DELTAZ;      % 检波器物理位置

        % 找到对应的DAS标距（应该是完全重合的）
        das_index = 2 + (i-1) * 5;  % 标距2,7,12,17,22,27,32,37对应检波器1-8
        das_pos_meter = gauge_centers(das_index) * DELTAZ;

        fprintf('检波器%d (Z=%.3fm) ↔ DAS标距%d (Z=%.3fm)\n', ...
                i, rec_pos_meter, das_index, das_pos_meter);
    end
    fprintf('================================\n');
end

for it = 1:NSTEP
    
    try
    %% Compute stress sigma
    % Update normal stresses (sigmaxx, sigmayy, sigmazz)
    for k = 2:NZ
        for j = 2:NY
            for i = 1:NX-1
                value_dvx_dx = (vx(i+1,j,k) - vx(i,j,k)) * ONE_OVER_DELTAX;
                value_dvy_dy = (vy(i,j,k) - vy(i,j-1,k)) * ONE_OVER_DELTAY;
                value_dvz_dz = (vz(i,j,k) - vz(i,j,k-1)) * ONE_OVER_DELTAZ;
                
                memory_dvx_dx(i,j,k) = b_x_half(i) * memory_dvx_dx(i,j,k) + a_x_half(i) * value_dvx_dx;
                memory_dvy_dy(i,j,k) = b_y(j) * memory_dvy_dy(i,j,k) + a_y(j) * value_dvy_dy;
                memory_dvz_dz(i,j,k) = b_z(k) * memory_dvz_dz(i,j,k) + a_z(k) * value_dvz_dz;
                
                value_dvx_dx = value_dvx_dx / K_x_half(i) + memory_dvx_dx(i,j,k);
                value_dvy_dy = value_dvy_dy / K_y(j) + memory_dvy_dy(i,j,k);
                value_dvz_dz = value_dvz_dz / K_z(k) + memory_dvz_dz(i,j,k);
                
                sigmaxx(i,j,k) = sigmaxx(i,j,k) + DELTAT_lambdaplus2mu(i,j,k)*value_dvx_dx + DELTAT_lambda(i,j,k)*(value_dvy_dy + value_dvz_dz);
                sigmayy(i,j,k) = sigmayy(i,j,k) + DELTAT_lambda(i,j,k)*(value_dvx_dx + value_dvz_dz) + DELTAT_lambdaplus2mu(i,j,k)*value_dvy_dy;
                sigmazz(i,j,k) = sigmazz(i,j,k) + DELTAT_lambda(i,j,k)*(value_dvx_dx + value_dvy_dy) + DELTAT_lambdaplus2mu(i,j,k)*value_dvz_dz;
            end
        end
    end
    
    % Update sigmaxy
    for k = 1:NZ
        for j = 1:NY-1
            for i = 2:NX
                value_dvy_dx = (vy(i,j,k) - vy(i-1,j,k)) * ONE_OVER_DELTAX;
                value_dvx_dy = (vx(i,j+1,k) - vx(i,j,k)) * ONE_OVER_DELTAY;
                
                memory_dvy_dx(i,j,k) = b_x(i) * memory_dvy_dx(i,j,k) + a_x(i) * value_dvy_dx;
                memory_dvx_dy(i,j,k) = b_y_half(j) * memory_dvx_dy(i,j,k) + a_y_half(j) * value_dvx_dy;
                
                value_dvy_dx = value_dvy_dx / K_x(i) + memory_dvy_dx(i,j,k);
                value_dvx_dy = value_dvx_dy / K_y_half(j) + memory_dvx_dy(i,j,k);
                
                sigmaxy(i,j,k) = sigmaxy(i,j,k) + DELTAT_mu_xy_avg(i,j,k)*(value_dvy_dx + value_dvx_dy);
            end
        end
    end
    
    % Update sigmaxz and sigmayz
    for k = 1:NZ-1
        for j = 1:NY
            for i = 2:NX
                value_dvz_dx = (vz(i,j,k) - vz(i-1,j,k)) * ONE_OVER_DELTAX;
                value_dvx_dz = (vx(i,j,k+1) - vx(i,j,k)) * ONE_OVER_DELTAZ;
                
                memory_dvz_dx(i,j,k) = b_x(i) * memory_dvz_dx(i,j,k) + a_x(i) * value_dvz_dx;
                memory_dvx_dz(i,j,k) = b_z_half(k) * memory_dvx_dz(i,j,k) + a_z_half(k) * value_dvx_dz;
                
                value_dvz_dx = value_dvz_dx / K_x(i) + memory_dvz_dx(i,j,k);
                value_dvx_dz = value_dvx_dz / K_z_half(k) + memory_dvx_dz(i,j,k);
                
                sigmaxz(i,j,k) = sigmaxz(i,j,k) + DELTAT_mu_xz_avg(i,j,k) *(value_dvz_dx + value_dvx_dz);
            end
        end
        
        for j = 1:NY-1
            for i = 1:NX
                value_dvz_dy = (vz(i,j+1,k) - vz(i,j,k)) * ONE_OVER_DELTAY;
                value_dvy_dz = (vy(i,j,k+1) - vy(i,j,k)) * ONE_OVER_DELTAZ;
                
                memory_dvz_dy(i,j,k) = b_y_half(j) * memory_dvz_dy(i,j,k) + a_y_half(j) * value_dvz_dy;
                memory_dvy_dz(i,j,k) = b_z_half(k) * memory_dvy_dz(i,j,k) + a_z_half(k) * value_dvy_dz;
                
                value_dvz_dy = value_dvz_dy / K_y_half(j) + memory_dvz_dy(i,j,k);
                value_dvy_dz = value_dvy_dz / K_z_half(k) + memory_dvy_dz(i,j,k);
                
                sigmayz(i,j,k) = sigmayz(i,j,k) + DELTAT_mu_yz_avg(i,j,k) *(value_dvz_dy + value_dvy_dz);
            end
        end
    end
    
    %% Compute velocity
    % Update vx
    for k = 2:NZ
        for j = 2:NY
            for i = 2:NX
                value_dsigmaxx_dx = (sigmaxx(i,j,k) - sigmaxx(i-1,j,k)) * ONE_OVER_DELTAX;
                value_dsigmaxy_dy = (sigmaxy(i,j,k) - sigmaxy(i,j-1,k)) * ONE_OVER_DELTAY;
                value_dsigmaxz_dz = (sigmaxz(i,j,k) - sigmaxz(i,j,k-1)) * ONE_OVER_DELTAZ;
                
                memory_dsigmaxx_dx(i,j,k) = b_x(i) * memory_dsigmaxx_dx(i,j,k) + a_x(i) * value_dsigmaxx_dx;
                memory_dsigmaxy_dy(i,j,k) = b_y(j) * memory_dsigmaxy_dy(i,j,k) + a_y(j) * value_dsigmaxy_dy;
                memory_dsigmaxz_dz(i,j,k) = b_z(k) * memory_dsigmaxz_dz(i,j,k) + a_z(k) * value_dsigmaxz_dz;
                
                value_dsigmaxx_dx = value_dsigmaxx_dx / K_x(i) + memory_dsigmaxx_dx(i,j,k);
                value_dsigmaxy_dy = value_dsigmaxy_dy / K_y(j) + memory_dsigmaxy_dy(i,j,k);
                value_dsigmaxz_dz = value_dsigmaxz_dz / K_z(k) + memory_dsigmaxz_dz(i,j,k);
                
                vx(i,j,k) = vx(i,j,k) + DELTAT_over_rhox(i,j,k) *(value_dsigmaxx_dx + value_dsigmaxy_dy + value_dsigmaxz_dz);
            end
        end
    end
    
    % Update vy
    for k = 2:NZ
        for j = 1:NY-1
            for i = 1:NX-1
                value_dsigmaxy_dx = (sigmaxy(i+1,j,k) - sigmaxy(i,j,k)) * ONE_OVER_DELTAX;
                value_dsigmayy_dy = (sigmayy(i,j+1,k) - sigmayy(i,j,k)) * ONE_OVER_DELTAY;
                value_dsigmayz_dz = (sigmayz(i,j,k) - sigmayz(i,j,k-1)) * ONE_OVER_DELTAZ;
                
                memory_dsigmaxy_dx(i,j,k) = b_x_half(i) * memory_dsigmaxy_dx(i,j,k) + a_x_half(i) * value_dsigmaxy_dx;
                memory_dsigmayy_dy(i,j,k) = b_y_half(j) * memory_dsigmayy_dy(i,j,k) + a_y_half(j) * value_dsigmayy_dy;
                memory_dsigmayz_dz(i,j,k) = b_z(k) * memory_dsigmayz_dz(i,j,k) + a_z(k) * value_dsigmayz_dz;
                
                value_dsigmaxy_dx = value_dsigmaxy_dx / K_x_half(i) + memory_dsigmaxy_dx(i,j,k);
                value_dsigmayy_dy = value_dsigmayy_dy / K_y_half(j) + memory_dsigmayy_dy(i,j,k);
                value_dsigmayz_dz = value_dsigmayz_dz / K_z(k) + memory_dsigmayz_dz(i,j,k);
                
                vy(i,j,k) = vy(i,j,k) + DELTAT_over_rhoy(i,j,k)*(value_dsigmaxy_dx + value_dsigmayy_dy + value_dsigmayz_dz);
            end
        end
    end
    
    % Update vz
    for k = 1:NZ-1
        for j = 2:NY
            for i = 1:NX-1
                value_dsigmaxz_dx = (sigmaxz(i+1,j,k) - sigmaxz(i,j,k)) * ONE_OVER_DELTAX;
                value_dsigmayz_dy = (sigmayz(i,j,k) - sigmayz(i,j-1,k)) * ONE_OVER_DELTAY;
                value_dsigmazz_dz = (sigmazz(i,j,k+1) - sigmazz(i,j,k)) * ONE_OVER_DELTAZ;
                
                memory_dsigmaxz_dx(i,j,k) = b_x_half(i) * memory_dsigmaxz_dx(i,j,k) + a_x_half(i) * value_dsigmaxz_dx;
                memory_dsigmayz_dy(i,j,k) = b_y(j) * memory_dsigmayz_dy(i,j,k) + a_y(j) * value_dsigmayz_dy;
                memory_dsigmazz_dz(i,j,k) = b_z_half(k) * memory_dsigmazz_dz(i,j,k) + a_z_half(k) * value_dsigmazz_dz;
                
                value_dsigmaxz_dx = value_dsigmaxz_dx / K_x_half(i) + memory_dsigmaxz_dx(i,j,k);
                value_dsigmayz_dy = value_dsigmayz_dy / K_y(j) + memory_dsigmayz_dy(i,j,k);
                value_dsigmazz_dz = value_dsigmazz_dz / K_z_half(k) + memory_dsigmazz_dz(i,j,k);
                
                vz(i,j,k) = vz(i,j,k) + DELTAT_over_rhoz(i,j,k)*(value_dsigmaxz_dx + value_dsigmayz_dy + value_dsigmazz_dz);
            end
        end
    end
    
    %% Add source
    a = PI*PI*f0*f0;
    t = (it-1)*DELTAT;
    
    % First derivative of a Gaussian
    source_term = (1-factor * 2.0*a*(t-t0)^2)*exp(-a*(t-t0)^2);
    
    force_x = source_term;
    
    sigmaxx(ISOURCE, JSOURCE, KSOURCE) = sigmaxx(ISOURCE, JSOURCE, KSOURCE) + force_x * DELTAT / rho;
    sigmayy(ISOURCE, JSOURCE, KSOURCE) = sigmayy(ISOURCE, JSOURCE, KSOURCE) + force_x * DELTAT / rho;
    sigmazz(ISOURCE, JSOURCE, KSOURCE) = sigmazz(ISOURCE, JSOURCE, KSOURCE) + force_x * DELTAT / rho;
    %% Apply boundary conditions (Dirichlet)
    % xmin and xmax
    vx(1,:,:) = ZERO;
    vy(1,:,:) = ZERO;
    vz(1,:,:) = ZERO;
    vx(NX,:,:) = ZERO;
    vy(NX,:,:) = ZERO;
    vz(NX,:,:) = ZERO;
    
    % ymin and ymax
    vx(:,1,:) = ZERO;
    vy(:,1,:) = ZERO;
    vz(:,1,:) = ZERO;
    vx(:,NY,:) = ZERO;
    vy(:,NY,:) = ZERO;
    vz(:,NY,:) = ZERO;
    
    % zmin and zmax
    vx(:,:,1) = ZERO;
    vy(:,:,1) = ZERO;
    vz(:,:,1) = ZERO;
    vx(:,:,NZ) = ZERO;
    vy(:,:,NZ) = ZERO;
    vz(:,:,NZ) = ZERO;
    
    catch ME
   fprintf('Error at time step %d\n', it);
   fprintf('Error message: %s\n', ME.message);
   fprintf('Error identifier: %s\n', ME.identifier);
   rethrow(ME);
    end
  fprintf('time: %.4f seconds\n', it);  
    if mod(it, 10) == 0
        % Create the plot
%         plot_3d_slices(sigmaxx+sigmayy+sigmazz, 0.5, 0.5, 0.25)
        pcolor(squeeze(sigmaxx(ISOURCE, :, :))');
%         caxis([-5e-2 5e-2]);
        shading interp;
        grid off;
        colorbar;
        title(sprintf('Wavefield at Time Step %d (%.3f s)', it, it*DELTAT));
        xlabel('Y Index');
        ylabel('Z Index');
        
        % Capture the frame
        frame = getframe(fig);
        im = frame2im(frame);
        [imind, cm] = rgb2ind(im, 256);
        
        % Write to the GIF file
        if frame_count == 0
            imwrite(imind, cm, filename, 'gif', 'Loopcount', inf, 'DelayTime', delay_time);
        else
            imwrite(imind, cm, filename, 'gif', 'WriteMode', 'append', 'DelayTime', delay_time);
        end
        
        frame_count = frame_count + 1;
        drawnow;
    end
  rec(1,it)=sigmaxx(ISOURCE,JSOURCE,KSOURCE+300);
  rec(2,it)=sigmaxx(ISOURCE,JSOURCE,KSOURCE+315);
  rec(3,it)=sigmaxx(ISOURCE,JSOURCE,KSOURCE+330);
  rec(4,it)=sigmaxx(ISOURCE,JSOURCE,KSOURCE+345);
  rec(5,it)=sigmaxx(ISOURCE,JSOURCE,KSOURCE+360);
  rec(6,it)=sigmaxx(ISOURCE,JSOURCE,KSOURCE+375);
  rec(7,it)=sigmaxx(ISOURCE,JSOURCE,KSOURCE+390);
  rec(8,it)=sigmaxx(ISOURCE,JSOURCE,KSOURCE+405);

% DAS系统数据采集（仅在启用DAS时执行）
if enable_das
    % 说明：DAS通过测量光纤沿线的应变率变化来检测声波信号
    % 计算标距长度内的平均应变率
    for i = 1:num_das_points
        % 计算当前标距的中心位置（网格点）
        gauge_center_k = round(gauge_centers(i));

        % 计算标距的两个端点位置（网格点）
        gauge_half_length_grid = round(gauge_length/(2*DELTAZ));  % 标距半长度（网格点数）= 3个网格点
        gauge_start = max(1, gauge_center_k - gauge_half_length_grid);     % 标距起始点
        gauge_end = min(NZ, gauge_center_k + gauge_half_length_grid);      % 标距结束点

        % DAS应变率计算：标距内平均应变率
        % 应变率 = ∂vz/∂z，使用三维程序中的vz速度场
        total_strain_rate = 0;
        segment_count = 0;

        for k = gauge_start:(gauge_end-1)
            if k >= 1 && k < NZ
                % 计算Z方向应变率分量：(vz(k+1) - vz(k)) / DELTAZ
                strain_rate_segment = (vz(ISOURCE, JSOURCE, k+1) - vz(ISOURCE, JSOURCE, k)) / DELTAZ;
                total_strain_rate = total_strain_rate + strain_rate_segment;
                segment_count = segment_count + 1;
            end
        end

        % 计算平均应变率并存储
        if segment_count > 0
            das_strain_rate(i, it) = total_strain_rate / segment_count;
        else
            das_strain_rate(i, it) = 0;  % 避免除零错误
        end
    end
end

end

fprintf('Simulation completed successfully!\n');
fprintf('Final time: %.4f seconds\n', (NSTEP-1)*DELTAT);

% 程序运行结束后的完整数据保存
fprintf('\n=== 开始保存数据 ===\n');

% 生成时间戳文件名
current_time = char(datetime('now', 'Format', 'yyyyMMdd_HHmmss'));
data_filename = sprintf('DanJi_3D_Results_%s.mat', current_time);

% 计算物理深度信息
fprintf('正在计算物理深度信息...\n');

% 检波器物理深度计算
geophone_positions_grid = [300, 315, 330, 345, 360, 375, 390, 405];  % 检波器网格位置（相对KSOURCE）
geophone_depths = zeros(8, 1);
for i = 1:8
    geophone_depths(i) = (KSOURCE + geophone_positions_grid(i)) * DELTAZ;  % 转换为物理深度(米)
end

% DAS物理深度计算（如果启用DAS）
if enable_das
    das_depths = zeros(num_das_points, 1);
    for i = 1:num_das_points
        das_depths(i) = gauge_centers(i) * DELTAZ;  % 转换为物理深度(米)
    end

    % 保存完整数据（包含DAS）
    save(data_filename, ...
        'rec', ...                    % 检波器数据 [8 × NSTEP]
        'das_strain_rate', ...        % DAS应变率数据 [37 × NSTEP]
        'geophone_depths', ...        % 检波器物理深度 [8 × 1] 单位:米
        'das_depths', ...             % DAS标距物理深度 [37 × 1] 单位:米
        'gauge_centers', ...          % DAS标距中心位置（网格点）[37 × 1]
        'num_das_points', ...         % DAS标距点数量 = 37
        'gauge_length', ...           % 标距长度 = 0.06m
        'gauge_overlap', ...          % 重叠比例 = 0.5
        'enable_das', ...             % DAS系统启用状态 = true
        'DELTAX', 'DELTAY', 'DELTAZ', ... % 网格间距 [m]
        'DELTAT', 'NSTEP', ...        % 时间步长[s]和总时间步数
        'NX', 'NY', 'NZ', ...         % 网格尺寸
        'ISOURCE', 'JSOURCE', 'KSOURCE', ... % 震源网格位置
        'f0', ...                     % 震源频率 [Hz]
        'cp', 'cs', 'rho', ...        % 地层参数：P波速度[m/s]、S波速度[m/s]、密度[kg/m³]
        'vf_p', 'rho_f', ...          % 流体参数：P波速度[m/s]、密度[kg/m³]
        'radius', ...                 % 井孔半径 [m]
        'current_time');              % 数据生成时间

    fprintf('✓ 已保存检波器和DAS完整数据\n');

else
    % 保存检波器数据（不包含DAS）
    save(data_filename, ...
        'rec', ...                    % 检波器数据 [8 × NSTEP]
        'geophone_depths', ...        % 检波器物理深度 [8 × 1] 单位:米
        'enable_das', ...             % DAS系统启用状态 = false
        'DELTAX', 'DELTAY', 'DELTAZ', ... % 网格间距 [m]
        'DELTAT', 'NSTEP', ...        % 时间步长[s]和总时间步数
        'NX', 'NY', 'NZ', ...         % 网格尺寸
        'ISOURCE', 'JSOURCE', 'KSOURCE', ... % 震源网格位置
        'f0', ...                     % 震源频率 [Hz]
        'cp', 'cs', 'rho', ...        % 地层参数
        'vf_p', 'rho_f', ...          % 流体参数
        'radius', ...                 % 井孔半径 [m]
        'current_time');              % 数据生成时间

    fprintf('✓ 已保存检波器数据\n');
end

% 显示保存信息
fprintf('\n=== 数据保存完成 ===\n');
fprintf('文件名: %s\n', data_filename);
fprintf('保存位置: %s\n', fullfile(pwd, data_filename));
fprintf('文件大小: %.2f MB\n', dir(data_filename).bytes/1024/1024);

% 显示数据结构信息
fprintf('\n=== 数据结构信息 ===\n');
fprintf('检波器数据 (rec): %d × %d\n', size(rec,1), size(rec,2));
fprintf('检波器深度 (geophone_depths): %d × 1 (单位:米)\n', length(geophone_depths));

if enable_das
    fprintf('DAS数据 (das_strain_rate): %d × %d\n', size(das_strain_rate,1), size(das_strain_rate,2));
    fprintf('DAS深度 (das_depths): %d × 1 (单位:米)\n', length(das_depths));
end

% 显示深度范围
fprintf('\n=== 深度信息 ===\n');
fprintf('震源深度: %.3f米\n', KSOURCE * DELTAZ);
fprintf('检波器深度范围: %.3f - %.3f米\n', min(geophone_depths), max(geophone_depths));
if enable_das
    fprintf('DAS深度范围: %.3f - %.3f米\n', min(das_depths), max(das_depths));
end

% 显示检波器与DAS对应关系（如果启用DAS）
if enable_das
    fprintf('\n=== 检波器与DAS深度对应关系 ===\n');
    das_indices = [2, 7, 12, 17, 22, 27, 32, 37];  % 对应检波器的DAS标距编号
    for i = 1:8
        fprintf('检波器%d: %.3f米 ↔ DAS标距%d: %.3f米\n', ...
                i, geophone_depths(i), das_indices(i), das_depths(das_indices(i)));
    end
end

% 数据使用示例
fprintf('\n=== 数据使用示例 ===\n');
fprintf('load(''%s'');  %% 加载数据\n', data_filename);
fprintf('\n%% 绘制检波器数据:\n');
fprintf('figure;\n');
fprintf('for i = 1:8\n');
fprintf('    subplot(2,4,i);\n');
fprintf('    plot((0:NSTEP-1)*DELTAT*1000, rec(i,:));\n');
fprintf('    title(sprintf(''检波器%%d (深度%%.3fm)'', i, geophone_depths(i)));\n');
fprintf('    xlabel(''时间 (ms)''); ylabel(''振幅'');\n');
fprintf('end\n');

if enable_das
    fprintf('\n%% 绘制DAS数据:\n');
    fprintf('figure;\n');
    fprintf('imagesc((0:NSTEP-1)*DELTAT*1000, das_depths, das_strain_rate);\n');
    fprintf('colorbar; xlabel(''时间 (ms)''); ylabel(''深度 (m)'');\n');
    fprintf('title(''DAS应变率数据'');\n');

    fprintf('\n%% 对比检波器与DAS:\n');
    fprintf('figure;\n');
    fprintf('plot((0:NSTEP-1)*DELTAT*1000, rec(1,:), ''r-'', ''LineWidth'', 2);\n');
    fprintf('hold on;\n');
    fprintf('plot((0:NSTEP-1)*DELTAT*1000, das_strain_rate(2,:), ''b--'', ''LineWidth'', 2);\n');
    fprintf('legend(''检波器1'', ''DAS标距2'', ''Location'', ''best'');\n');
    fprintf('xlabel(''时间 (ms)''); ylabel(''振幅'');\n');
    fprintf('title(''检波器与DAS数据对比'');\n');
end

fprintf('\n=== 三维单极子正演模拟完成 ===\n');

% 显示保存信息
fprintf('\n=== 数据保存完成 ===\n');
fprintf('文件名: %s\n', data_filename);
fprintf('保存位置: %s\n', fullfile(pwd, data_filename));
fprintf('文件大小: %.2f MB\n', dir(data_filename).bytes/1024/1024);

% 显示数据结构信息
fprintf('\n=== 数据结构信息 ===\n');
fprintf('检波器数据 (rec): %d × %d\n', size(rec,1), size(rec,2));
fprintf('检波器深度 (geophone_depths): %d × 1 (单位:米)\n', length(geophone_depths));

if enable_das
    fprintf('DAS数据 (das_strain_rate): %d × %d\n', size(das_strain_rate,1), size(das_strain_rate,2));
    fprintf('DAS深度 (das_depths): %d × 1 (单位:米)\n', length(das_depths));
end

% 显示深度范围
fprintf('\n=== 深度信息 ===\n');
fprintf('震源深度: %.3f米\n', KSOURCE * DELTAZ);
fprintf('检波器深度范围: %.3f - %.3f米\n', min(geophone_depths), max(geophone_depths));
if enable_das
    fprintf('DAS深度范围: %.3f - %.3f米\n', min(das_depths), max(das_depths));
end

% 显示检波器与DAS对应关系（如果启用DAS）
if enable_das
    fprintf('\n=== 检波器与DAS深度对应关系 ===\n');
    das_indices = [2, 7, 12, 17, 22, 27, 32, 37];  % 对应检波器的DAS标距编号
    for i = 1:8
        fprintf('检波器%d: %.3f米 ↔ DAS标距%d: %.3f米\n', ...
                i, geophone_depths(i), das_indices(i), das_depths(das_indices(i)));
    end
end

% 数据使用示例
fprintf('\n=== 数据使用示例 ===\n');
fprintf('load(''%s'');  %% 加载数据\n', data_filename);
fprintf('\n%% 绘制检波器数据:\n');
fprintf('figure;\n');
fprintf('for i = 1:8\n');
fprintf('    subplot(2,4,i);\n');
fprintf('    plot((0:NSTEP-1)*DELTAT*1000, rec(i,:));\n');
fprintf('    title(sprintf(''检波器%%%%d (深度%%.3fm)'', i, geophone_depths(i)));\n');
fprintf('    xlabel(''时间 (ms)''); ylabel(''振幅'');\n');
fprintf('end\n');

if enable_das
    fprintf('\n%% 绘制DAS数据:\n');
    fprintf('figure;\n');
    fprintf('imagesc((0:NSTEP-1)*DELTAT*1000, das_depths, das_strain_rate);\n');
    fprintf('colorbar; xlabel(''时间 (ms)''); ylabel(''深度 (m)'');\n');
    fprintf('title(''DAS应变率数据'');\n');

    fprintf('\n%% 对比检波器与DAS:\n');
    fprintf('figure;\n');
    fprintf('plot((0:NSTEP-1)*DELTAT*1000, rec(1,:), ''r-'', ''LineWidth'', 2);\n');
    fprintf('hold on;\n');
    fprintf('plot((0:NSTEP-1)*DELTAT*1000, das_strain_rate(2,:), ''b--'', ''LineWidth'', 2);\n');
    fprintf('legend(''检波器1'', ''DAS标距2'', ''Location'', ''best'');\n');
    fprintf('xlabel(''时间 (ms)''); ylabel(''振幅'');\n');
    fprintf('title(''检波器与DAS数据对比'');\n');
end

fprintf('\n=== 三维单极子正演模拟完成 ===\n');

